<!-- Deposit Modal -->
<div id="deposit-modal" class="modal" role="dialog" aria-labelledby="deposit-title" aria-hidden="true">
    <div class="modal-content">
        <button class="close" aria-label="Close modal">&times;</button>
        <h2 id="deposit-title">💳 Deposit KMS Credit</h2>
        
        <div class="form-group">
            <label>Select Amount:</label>
            <div class="amount-buttons">
                <button class="amount-btn" data-amount="10">$10</button>
                <button class="amount-btn" data-amount="25">$25</button>
                <button class="amount-btn" data-amount="50">$50</button>
                <button class="amount-btn" data-amount="100">$100</button>
                <button class="amount-btn" data-amount="250">$250</button>
                <button class="amount-btn" data-amount="500">$500</button>
                <button class="amount-btn" data-amount="1000">$1000</button>
                <button class="amount-btn" data-amount="2500">$2500</button>
                <button class="amount-btn" data-amount="5000">$5000</button>
                <button class="amount-btn" data-amount="9500">$9500</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="custom-deposit-amount">Or enter custom amount:</label>
            <input type="number" id="custom-deposit-amount" min="1" max="9500" step="0.01" placeholder="Enter amount">
        </div>
        
        <div class="form-group">
            <label>Payment Method:</label>
            <div class="payment-methods-row">
                <button type="button" class="payment-method-btn" data-method="paypal">
                    <span class="payment-icon">💙</span>
                    <span class="payment-text">PayPal</span>
                </button>
                <button type="button" class="payment-method-btn" data-method="stripe">
                    <span class="payment-icon">💳</span>
                    <span class="payment-text">Credit Card</span>
                </button>
                <button type="button" class="payment-method-btn" data-method="square">
                    <span class="payment-icon">⬜</span>
                    <span class="payment-text">Square</span>
                </button>
                <button type="button" class="payment-method-btn" data-method="venmo">
                    <span class="payment-icon">📱</span>
                    <span class="payment-text">Venmo</span>
                </button>
                <button type="button" class="payment-method-btn" data-method="zelle">
                    <span class="payment-icon">⚡</span>
                    <span class="payment-text">Zelle</span>
                </button>
                <button type="button" class="payment-method-btn" data-method="debit">
                    <span class="payment-icon">💳</span>
                    <span class="payment-text">Debit Card</span>
                </button>
            </div>
            <input type="hidden" id="deposit-method" value="">
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-deposit">Process Deposit</button>
        </div>
    </div>
</div>

<!-- Withdraw Modal -->
<div id="withdraw-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>💰 Withdraw KMS Credit</h2>
        
        <div class="form-group">
            <label>Available Balance: <span id="withdraw-balance">$0.00</span></label>
        </div>
        
        <div class="form-group">
            <label>Select Amount:</label>
            <div class="amount-buttons">
                <button class="amount-btn" data-amount="5">$5</button>
                <button class="amount-btn" data-amount="10">$10</button>
                <button class="amount-btn" data-amount="25">$25</button>
                <button class="amount-btn" data-amount="50">$50</button>
                <button class="amount-btn" data-amount="100">$100</button>
                <button class="amount-btn" data-amount="250">$250</button>
            </div>
        </div>
        
        <div class="form-group">
            <label for="custom-withdraw-amount">Or enter custom amount:</label>
            <input type="number" id="custom-withdraw-amount" min="5" step="0.01" placeholder="Minimum $5">
        </div>
        
        <div class="form-group">
            <label>Withdrawal Method:</label>
            <div class="payment-methods">
                <button type="button" class="payment-method-btn" data-method="paypal">
                    <div class="payment-icon">💳</div>
                    PayPal
                </button>
                <button type="button" class="payment-method-btn" data-method="venmo">
                    <div class="payment-icon">📱</div>
                    Venmo
                </button>
                <button type="button" class="payment-method-btn" data-method="zelle">
                    <div class="payment-icon">⚡</div>
                    Zelle
                </button>
                <button type="button" class="payment-method-btn" data-method="bank_transfer">
                    <div class="payment-icon">🏦</div>
                    Bank Transfer
                </button>
            </div>
            <input type="hidden" id="withdraw-method" value="">
        </div>
        
        <div class="form-group">
            <label for="withdraw-details">Payment Details:</label>
            <textarea id="withdraw-details" placeholder="Enter your payment account details (email, phone, account number, etc.)"></textarea>
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-withdraw">Request Withdrawal</button>
        </div>
        
        <div class="form-group">
            <small>Note: Withdrawals are processed within 1-3 business days. Minimum withdrawal amount is $5.</small>
        </div>
    </div>
</div>

<!-- Transfer Modal -->
<div id="transfer-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>🔄 Transfer Commission to KMS Credit</h2>
        
        <div class="form-group">
            <label>Available Commission Balance: <span id="commission-balance">$0.00</span></label>
        </div>
        
        <div class="form-group">
            <label for="transfer-amount">Transfer Amount:</label>
            <input type="number" id="transfer-amount" min="1" step="0.01" placeholder="Enter amount to transfer">
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="process-transfer">Transfer to KMS Credit</button>
        </div>
        
        <div class="form-group">
            <small>Note: Commission balance can be transferred to KMS Credit for use on services and PC builds.</small>
        </div>
    </div>
</div>

<style>
.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(65px, 1fr));
    gap: 10px;
    margin: 10px 0;
}

.amount-btn {
    padding: 10px;
    border: 2px solid #ffd400;
    background: #ffeb00;
    color: #ffffff;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 2px 4px 6px rgba(0,0,0,0.6);
}

.amount-btn.hover {
    background: #00f3ff;
    border: 2px solid #00f3ff;
    color: white;
}

.amount-btn.selected {
    background: #00f3ff;
    color: white;
}

.payment-methods {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 10px 0;
}

.payment-method-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 50px;
    min-height: 100px;
    border: 2px solid #00bcaa;
    background: rgba(0, 255, 229, 0.7);
    color: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.payment-method-btn:hover {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.65);
    transform: translateY(-2px);
}

.payment-method-btn.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 1);
    box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.6);
}

.payment-icon {
    font-size: 24px;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    overflow-y: auto;
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    position: absolute;
    right: 15px;
    top: 10px;
}

.close:hover {
    color: white;
}

.form-group {
    margin: 15px 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #00ffff;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #ffffff82;
    border-radius: 10px;
    background: rgb(255,196,0);
    color: #ffffff;
    box-sizing: border-box;
    box-shadow: 2px 4px 6px rgba(0,0,0,0.6);
}

.form-group textarea {
    background-color: #ffc000;
    resize: vertical;
    min-height: 80px;
}

.btn-primary {
    display: block;
    margin-left: auto;
    margin-right: auto;
    background: #ffc400;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    width: 35%;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }
    
    .amount-buttons {
        grid-template-columns: repeat(3, 1fr);
    }
}
</style>

<script>
// Amount button functionality
document.addEventListener('DOMContentLoaded', function() {
    // Handle amount button clicks
    document.querySelectorAll('.amount-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const amount = this.dataset.amount;
            const modal = this.closest('.modal');
            const customInput = modal.querySelector('input[type="number"]');
            
            // Remove selected class from all buttons in this modal
            modal.querySelectorAll('.amount-btn').forEach(b => b.classList.remove('selected'));
            
            // Add selected class to clicked button
            this.classList.add('selected');
            
            // Set the custom input value
            if (customInput) {
                customInput.value = amount;
            }
        });
    });
    
    // Handle custom amount input
    document.querySelectorAll('input[type="number"]').forEach(input => {
        input.addEventListener('input', function() {
            const modal = this.closest('.modal');
            if (modal) {
                modal.querySelectorAll('.amount-btn').forEach(btn => btn.classList.remove('selected'));
            }
        });
    });

    // Handle payment method button clicks
    document.querySelectorAll('.payment-method-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            const method = this.dataset.method;
            const hiddenInput = modal.querySelector('input[type="hidden"]');

            // Remove selected class from all payment buttons in this modal
            modal.querySelectorAll('.payment-method-btn').forEach(b => b.classList.remove('selected'));

            // Add selected class to clicked button
            this.classList.add('selected');

            // Set the hidden input value
            if (hiddenInput) {
                hiddenInput.value = method;
            }
        });
    });
    
    // Process deposit button
    const processDepositBtn = document.getElementById('process-deposit');
    if (processDepositBtn) {
        processDepositBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            const amount = parseFloat(modal.querySelector('#custom-deposit-amount').value);
            const method = modal.querySelector('#deposit-method').value;

            // Validate inputs
            if (isNaN(amount) || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            if (!method) {
                alert('Please select a payment method');
                return;
            }

            // Process deposit via API
            if (typeof processDeposit === 'function') {
                processDeposit(amount, method);
            } else {
                console.log('Processing deposit:', { amount, method });
                alert(`Deposit of $${amount.toFixed(2)} via ${method} initiated successfully!`);
            }

            // Close modal
            modal.style.display = 'none';
        });
    }

    // Process withdraw button
    const processWithdrawBtn = document.getElementById('process-withdraw');
    if (processWithdrawBtn) {
        processWithdrawBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            const amount = parseFloat(modal.querySelector('#custom-withdraw-amount').value);
            const method = modal.querySelector('#withdraw-method').value;
            const details = modal.querySelector('#withdraw-details').value;

            // Validate inputs
            if (isNaN(amount) || amount < 5) {
                alert('Please enter a valid amount (minimum $5)');
                return;
            }

            if (!method) {
                alert('Please select a withdrawal method');
                return;
            }

            if (!details.trim()) {
                alert('Please enter your payment details');
                return;
            }

            // Check if sufficient balance
            const currentBalance = parseFloat(document.getElementById('walletBalance').textContent.replace('$', ''));
            if (amount > currentBalance) {
                alert('Insufficient balance');
                return;
            }

            // Process withdrawal via API
            if (typeof processWithdrawal === 'function') {
                processWithdrawal(amount, method, details);
            } else {
                console.log('Processing withdrawal:', { amount, method, details });
                alert(`Withdrawal request of $${amount.toFixed(2)} via ${method} submitted successfully! Processing time: 1-3 business days.`);
            }

            // Close modal
            modal.style.display = 'none';
        });
    }
    
    // Process transfer button
    const processTransferBtn = document.getElementById('process-transfer');
    if (processTransferBtn) {
        processTransferBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            const amount = parseFloat(modal.querySelector('#transfer-amount').value);

            // Validate inputs
            if (isNaN(amount) || amount <= 0) {
                alert('Please enter a valid amount');
                return;
            }

            // Process transfer via API
            if (typeof processCommissionTransfer === 'function') {
                processCommissionTransfer(amount);
            } else {
                console.log('Processing transfer:', { amount });
                alert(`Transfer of $${amount.toFixed(2)} to KMS Credit completed successfully!`);
            }

            // Close modal
            modal.style.display = 'none';
        });
    }
});
</script>