[18-Jul-2025 14:26:42 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:26:42 Europe/Berlin] [2025-07-18 14:26:42] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[18-Jul-2025 14:26:42 Europe/Berlin] Request method: GET
[18-Jul-2025 14:26:42 Europe/Berlin] Session ID: none
[18-Jul-2025 14:26:42 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:26:42 Europe/Berlin] [2025-07-18 14:26:42] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[18-Jul-2025 14:26:42 Europe/Berlin] Request method: GET
[18-Jul-2025 14:26:42 Europe/Berlin] Session ID: none
[18-Jul-2025 14:26:42 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[18-Jul-2025 14:26:52 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:26:52 Europe/Berlin] [2025-07-18 14:26:52] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[18-Jul-2025 14:26:52 Europe/Berlin] Request method: GET
[18-Jul-2025 14:26:52 Europe/Berlin] Session ID: none
[18-Jul-2025 14:26:53 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:26:53 Europe/Berlin] [2025-07-18 14:26:53] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[18-Jul-2025 14:26:53 Europe/Berlin] Request method: GET
[18-Jul-2025 14:26:53 Europe/Berlin] Session ID: none
[18-Jul-2025 14:26:53 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[18-Jul-2025 14:35:33 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:35:33 Europe/Berlin] [2025-07-18 14:35:33] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[18-Jul-2025 14:35:33 Europe/Berlin] Request method: GET
[18-Jul-2025 14:35:33 Europe/Berlin] Session ID: none
[18-Jul-2025 14:35:33 Europe/Berlin] 
================================================================================
[18-Jul-2025 14:35:33 Europe/Berlin] [2025-07-18 14:35:33] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[18-Jul-2025 14:35:33 Europe/Berlin] Request method: GET
[18-Jul-2025 14:35:33 Europe/Berlin] Session ID: none
[18-Jul-2025 14:35:33 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 00:01:04 Europe/Berlin] 
================================================================================
[19-Jul-2025 00:01:04 Europe/Berlin] [2025-07-19 00:01:04] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 00:01:04 Europe/Berlin] Request method: GET
[19-Jul-2025 00:01:04 Europe/Berlin] Session ID: none
[19-Jul-2025 00:01:04 Europe/Berlin] 
================================================================================
[19-Jul-2025 00:01:04 Europe/Berlin] [2025-07-19 00:01:04] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 00:01:04 Europe/Berlin] Request method: GET
[19-Jul-2025 00:01:04 Europe/Berlin] Session ID: none
[19-Jul-2025 00:01:04 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 00:14:40 Europe/Berlin] 
================================================================================
[19-Jul-2025 00:14:40 Europe/Berlin] [2025-07-19 00:14:40] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 00:14:40 Europe/Berlin] Request method: GET
[19-Jul-2025 00:14:40 Europe/Berlin] Session ID: none
[19-Jul-2025 00:14:40 Europe/Berlin] 
================================================================================
[19-Jul-2025 00:14:40 Europe/Berlin] [2025-07-19 00:14:40] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 00:14:40 Europe/Berlin] Request method: GET
[19-Jul-2025 00:14:40 Europe/Berlin] Session ID: none
[19-Jul-2025 00:14:40 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 01:00:46 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:00:46 Europe/Berlin] [2025-07-19 01:00:46] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 01:00:46 Europe/Berlin] Request method: GET
[19-Jul-2025 01:00:46 Europe/Berlin] Session ID: none
[19-Jul-2025 01:00:46 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:00:46 Europe/Berlin] [2025-07-19 01:00:46] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 01:00:46 Europe/Berlin] Request method: GET
[19-Jul-2025 01:00:46 Europe/Berlin] Session ID: none
[19-Jul-2025 01:00:46 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 01:04:06 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:04:06 Europe/Berlin] [2025-07-19 01:04:06] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 01:04:06 Europe/Berlin] Request method: GET
[19-Jul-2025 01:04:06 Europe/Berlin] Session ID: none
[19-Jul-2025 01:04:06 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:04:06 Europe/Berlin] [2025-07-19 01:04:06] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 01:04:06 Europe/Berlin] Request method: GET
[19-Jul-2025 01:04:06 Europe/Berlin] Session ID: none
[19-Jul-2025 01:04:06 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 01:17:09 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:17:09 Europe/Berlin] [2025-07-19 01:17:09] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 01:17:09 Europe/Berlin] Request method: GET
[19-Jul-2025 01:17:09 Europe/Berlin] Session ID: none
[19-Jul-2025 01:17:09 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:17:09 Europe/Berlin] [2025-07-19 01:17:09] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 01:17:09 Europe/Berlin] Request method: GET
[19-Jul-2025 01:17:09 Europe/Berlin] Session ID: none
[19-Jul-2025 01:17:09 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

[19-Jul-2025 01:46:52 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:46:52 Europe/Berlin] [2025-07-19 01:46:52] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_info
[19-Jul-2025 01:46:52 Europe/Berlin] Request method: GET
[19-Jul-2025 01:46:52 Europe/Berlin] Session ID: none
[19-Jul-2025 01:46:52 Europe/Berlin] 
================================================================================
[19-Jul-2025 01:46:52 Europe/Berlin] [2025-07-19 01:46:52] Starting API request: /KelvinKMS.com/KMS_Apps/KMS_Member/KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_stats
[19-Jul-2025 01:46:52 Europe/Berlin] Request method: GET
[19-Jul-2025 01:46:52 Europe/Berlin] Session ID: none
[19-Jul-2025 01:46:52 Europe/Berlin] API Error: Array
(
    [message] => Table 'kelvinkms.affiliate_stats' doesn't exist
    [file] => D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php
    [line] => 50
    [trace] => #0 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Core\KMS_Config\KMS_PHP\KMS_config.php(50): mysqli_prepare(Object(mysqli), 'SELECT * FROM a...')
#1 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_system.php(339): execute_query(Object(mysqli), 'SELECT * FROM a...', 'i', Array)
#2 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(213): AffiliateSystem->getAffiliateStats(2)
#3 D:\xampp\htdocs\KelvinKMS.com\KMS_Apps\KMS_Member\KMS_Affiliate\KMS_PHP\KMS_affiliate_api.php(109): getAffiliateStats(Object(AffiliateSystem), 2)
#4 {main}
    [session] => Array
        (
            [loggedin] => 1
            [id] => 2
            [username] => KelvinKMS
            [is_admin] => 
            [test] => working
            [language] => en
        )

)

