<!-- Order Confirmation Modal -->
<div id="order-confirmation-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>📋 Order Confirmation</h2>
        
        <div id="order-details">
            <!-- Order details will be populated by JavaScript -->
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="confirm-order">Confirm Order</button>
            <button class="btn-secondary" id="cancel-order">Cancel</button>
        </div>
    </div>
</div>

<!-- PC Build Configuration Modal -->
<div id="pc-config-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>🖥️ PC Configuration</h2>
        
        <div id="pc-config-details">
            <!-- PC configuration details will be populated by JavaScript -->
        </div>
        
        <div class="form-group">
            <label for="pc-config-notes">Special Requirements:</label>
            <textarea id="pc-config-notes" placeholder="Any special requirements or modifications..."></textarea>
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="confirm-pc-build">Confirm PC Build</button>
            <button class="btn-secondary" id="cancel-pc-build">Cancel</button>
        </div>
    </div>
</div>

<!-- Service Upload Modal -->
<div id="service-upload-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>📁 Upload Files for Service</h2>
        
        <div class="form-group">
            <label for="service-files">Select Files:</label>
            <input type="file" id="service-files" multiple accept="image/*,video/*,.pdf,.doc,.docx">
        </div>
        
        <div class="form-group">
            <label for="upload-instructions">Instructions:</label>
            <textarea id="upload-instructions" placeholder="Please provide specific instructions for your files..."></textarea>
        </div>
        
        <div id="upload-progress" style="display: none;">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text" id="progress-text">0%</div>
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="start-upload">Upload Files</button>
            <button class="btn-secondary" id="cancel-upload">Cancel</button>
        </div>
    </div>
</div>

<!-- Order Status Modal -->
<div id="order-status-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>📊 Order Status</h2>
        
        <div id="order-status-content">
            <!-- Order status will be populated by JavaScript -->
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="close-status">Close</button>
        </div>
    </div>
</div>

<!-- Payment Method Selection Modal -->
<div id="payment-method-modal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2>💳 Select Payment Method</h2>
        
        <div class="payment-methods">
            <div class="payment-option" data-method="kms_credit">
                <div class="payment-icon">💰</div>
                <div class="payment-info">
                    <h4>KMS Credit</h4>
                    <p>Use your available KMS Credit balance</p>
                    <div class="payment-balance">Available: <span id="kms-credit-balance">$0.00</span></div>
                </div>
            </div>
            
            <div class="payment-option" data-method="paypal">
                <div class="payment-icon">🅿️</div>
                <div class="payment-info">
                    <h4>PayPal</h4>
                    <p>Pay securely with PayPal</p>
                </div>
            </div>
            
            <div class="payment-option" data-method="stripe">
                <div class="payment-icon">💳</div>
                <div class="payment-info">
                    <h4>Credit/Debit Card</h4>
                    <p>Pay with credit or debit card via Stripe</p>
                </div>
            </div>
            
            <div class="payment-option" data-method="square">
                <div class="payment-icon">⬜</div>
                <div class="payment-info">
                    <h4>Square</h4>
                    <p>Pay with Square payment system</p>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <button class="btn-primary" id="select-payment-method" disabled>Select Payment Method</button>
        </div>
    </div>
</div>

<style>
.btn-secondary {
    background: linear-gradient(145deg, #6c757d, #5a6268);
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    margin-left: 10px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.progress-bar {
    width: 100%;
    height: 20px;
    background-color: rgba(255,255,255,0.2);
    border-radius: 10px;
    overflow: hidden;
    margin: 10px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(145deg, #4CAF50, #45a049);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #00ffff;
    font-weight: bold;
}

.payment-methods {
    display: grid;
    gap: 15px;
    margin: 20px 0;
}

.payment-option {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid rgba(255,255,255,0.2);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-option:hover {
    border-color: rgba(255,255,255,0.4);
    background: rgba(255,255,255,0.05);
}

.payment-option.selected {
    border-color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
}

.payment-icon {
    font-size: 30px;
    margin-left: 10px;
    width: 40px;
    text-align: center;
}

.payment-info h4 {
    margin: 0 0 5px 0;
    color: #00ffff;
}

.payment-info p {
    margin: 0;
    color: #ccc;
    font-size: 14px;
}

.payment-balance {
    color: #ffd700;
    font-weight: bold;
    font-size: 14px;
    margin-top: 5px;
}

#order-details,
#pc-config-details,
#order-status-content {
    background: rgba(0,0,0,0.2);
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.order-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.order-item:last-child {
    border-bottom: none;
}

.order-item-name {
    font-weight: bold;
    color: #00ffff;
}

.order-item-quantity {
    color: #ccc;
}

.order-item-price {
    color: #ffd700;
    font-weight: bold;
}

.order-total-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-top: 2px solid rgba(255,255,255,0.2);
    margin-top: 10px;
    font-size: 18px;
    font-weight: bold;
}

.order-total-label {
    color: #00ffff;
}

.order-total-amount {
    color: #ffd700;
}

@media (max-width: 768px) {
    .payment-option {
        flex-direction: column;
        text-align: center;
    }
    
    .payment-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .btn-secondary {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Payment method selection
    document.querySelectorAll('.payment-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            document.querySelectorAll('.payment-option').forEach(opt => opt.classList.remove('selected'));
            
            // Add selected class to clicked option
            this.classList.add('selected');
            
            // Enable the select button
            document.getElementById('select-payment-method').disabled = false;
        });
    });
    
    // File upload handling
    document.getElementById('service-files')?.addEventListener('change', function() {
        const files = this.files;
        if (files.length > 0) {
            console.log(`Selected ${files.length} file(s)`);
        }
    });
    
    // Upload progress simulation
    function simulateUpload() {
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');
        const progressContainer = document.getElementById('upload-progress');
        
        progressContainer.style.display = 'block';
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress >= 100) {
                progress = 100;
                clearInterval(interval);
            }
            
            progressFill.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';
        }, 200);
    }
    
    // Start upload button
    document.getElementById('start-upload')?.addEventListener('click', function() {
        const files = document.getElementById('service-files').files;
        if (files.length === 0) {
            alert('Please select files to upload.');
            return;
        }
        
        simulateUpload();
    });
});
</script>
