/* KMS Member Dashboard Styles - Consolidated CSS */
body {
    font-family: Arial, sans-serif;
    background-color: #e1c210;
    color: white;
    margin: 0;
    padding: 20px;
    position: relative;
}

.container {
    max-width: 1000px;
    margin: auto;
    background-color: #2b9869;
    padding: 20px;
    border-radius: 14px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
}

/* Top Controls */
.top-controls {
    position: fixed;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

/* Live Chat Button */
.live-chat-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.btn-chat {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #00bcaa;
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.6);
    transition: all 0.3s ease;
}

.btn-chat:hover {
    background-color: #ffbf00;
    transform: scale(1.1);
}

h1,
h2 {
    color: #00ffff;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

h3,
h4,
h5,
h6 {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

p,
div,
span {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
}

#welcome-msg {
    text-align: center;
    font-size: 24px;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Wallet Section Styles */
.wallet-section {
    margin: 20px 0;
}

.wallet-card {
    background: rgba(19, 73, 240, 0.4);
    padding: 10px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.wallet-balance {
    text-align: center;
    margin-bottom: 20px;
}

.balance-amount {
    font-size: 36px;
    font-weight: bold;
    color: #ffd700;
    margin: 10px 0;
}

.wallet-stats {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-item {
    text-align: center;
    flex: 1;
    min-width: 150px;
}

.stat-item h4 {
    color: #00ffff;
    margin: 0 0 10px 0;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 5px 0;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-value::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.stat-value:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
}

.stat-value:hover::before {
    left: 100%;
}

.wallet-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

/* Button Styles */
button {
    cursor: pointer;
    padding: 10px 15px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.btn-deposit {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-withdraw {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

.btn-transfer {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-primary {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-success {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-warning {
    background: linear-gradient(145deg, #ff9800, #e68900);
    color: white;
}

.btn-danger {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

/* Service Section Styles */
.service-section {
    margin: 30px 0;
    background: rgba(0, 0, 0, 0.2);
    padding: 20px;
    border-radius: 10px;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.service-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.service-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-5px);
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.service-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: bold;
    color: #00ffff;
}

.service-price {
    color: #ffd700;
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #2196F3;
    color: white;
    border: none;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-input {
    width: 60px;
    text-align: center;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
}

/* Order Summary Styles */
.order-summary {
    background: rgba(0, 0, 0, 0.3);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.order-total {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    text-align: center;
    margin: 20px 0;
}

/* PC Builder Styles */
.pc-builder-section {
    margin: 30px 0;
}

.mode-selector {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
    transform: scale(1.05);
}

.mode-btn:not(.active) {
    background: rgba(255, 255, 255, 0.2);
    color: #ccc;
}

.pc-component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.component-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.component-select {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    margin-top: 10px;
}

/* Enhanced PC Builder Styles */
.pc-option-card,
.prebuilt-config-card {
    background: rgba(0, 188, 170, 0.2);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.pc-option-card:hover,
.prebuilt-config-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.pc-option-card.selected,
.prebuilt-config-card.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.pc-option-card h4,
.prebuilt-config-card h4 {
    color: #00ffff;
    margin-top: 0;
    font-size: 18px;
}

.price-range,
.config-price {
    font-size: 20px;
    font-weight: bold;
    color: #ffbf00;
    margin: 10px 0;
}

.specs-list,
.config-specs {
    list-style-type: none;
    padding-left: 0;
    margin: 15px 0;
}

.specs-list li,
.config-specs li {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-list li:last-child,
.config-specs li:last-child {
    border-bottom: none;
}

.color-option,
.config-description {
    margin: 10px 0;
    font-style: italic;
}

/* Component Category Styles */
.component-category {
    margin-bottom: 30px;
}

.component-category h4 {
    color: #00ffff;
    border-bottom: 2px solid #00bcaa;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.component-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.component-option {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.component-option:hover {
    background: rgba(0, 0, 0, 0.4);
}

.component-option.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
}

.component-name {
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}

.component-price {
    color: #ffbf00;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.component-description {
    color: #ccc;
    font-size: 14px;
    margin-bottom: 10px;
}

.select-option-btn,
.select-component-btn,
.select-config-btn {
    width: 100%;
    margin-top: 10px;
}

/* Affiliate Section Styles */
.affiliate-section {
    margin: 30px 0;
}

.commission-rate-display {
    background: rgb(255 173 0);
    border-radius: 10px;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.commission-rate {
    font-size: 20px;
    font-weight: bold;
    color: #ffc600;
    background: rgb(126 12 12);
    padding: 10px 15px;
    border-radius: 10px;
    display: inline-block;
    box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.6);
}

.commission-note {
    color: #ffffff;
    font-size: 20px;
}

.affiliate-code-display {
    background: rgb(255 0 102);
    padding: 10px;
    border-radius: 10px;
    text-align: center;
    margin: 5px 0;
}

.affiliate-code {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: rgba(255, 215, 0, 0.1);
    padding: 10px 20px;
    border-radius: 5px;
    display: inline-block;
    margin: 10px 0;
}

.referral-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.referral-stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

/* Modal and Form Styles - Moved to enhanced versions below */

/* Top Control Buttons */
.btn-settings,
.btn-logout {
    padding: 8px 15px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: 2px 4px 6px rgba(0, 0, 0, 0.6);
}

.btn-settings {
    background-color: #00bcaa;
    color: white;
}

.btn-settings:hover {
    background-color: #ffbf00;
}

.btn-logout {
    background-color: #e74c3c;
    color: white;
}

.btn-logout:hover {
    background-color: #c0392b;
}

/* Affiliate Actions */
.affiliate-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.affiliate-actions .btn-primary,
.affiliate-actions .btn-secondary {
    width: auto;
    min-width: 150px;
    max-width: 200px;
    padding: 10px 15px;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Scroll to section function */
html {
    scroll-behavior: smooth;
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background-color: #4CAF50;
}

.notification-error {
    background-color: #f44336;
}

.notification-info {
    background-color: #2196F3;
}

.notification-warning {
    background-color: #ff9800;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin-left: 10px;
}

.notification-close:hover {
    opacity: 0.7;
}

@keyframes slideIn {
    from {
        transform: translate3d(100%, 0, 0);
        opacity: 0;
    }

    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

/* Optimized animation with will-change for better performance */
.notification {
    will-change: transform, opacity;
}

.notification.animating {
    animation: slideIn 0.3s ease-out;
}

.notification:not(.animating) {
    will-change: auto;
}

/* Force hardware acceleration for better performance */
.notification {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* Optimized hardware acceleration */
.notification {
    will-change: auto;
    /* Only set will-change when animating */
    backface-visibility: hidden;
    transform: translateZ(0);
    /* Force layer creation */
}

/* Add animation trigger class */
.notification.animating {
    will-change: transform, opacity;
}

/* Performance optimizations for frequently animated elements */
.stat-value,
.option-btn,
.service-card,
.component-option {
    transform: translateZ(0);
    /* Create composite layer */
    backface-visibility: hidden;
}

/* Reduce paint operations on hover */
.stat-value:hover,
.option-btn:hover,
.service-card:hover,
.component-option:hover {
    will-change: transform;
}

.stat-value:not(:hover),
.option-btn:not(:hover),
.service-card:not(:hover),
.component-option:not(:hover) {
    will-change: auto;
}

/* Payment Method Buttons - Moved to KMS_wallet.css */

/* Modal Enhancements */
.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.withdraw-info {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.withdraw-info p {
    margin: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .wallet-stats {
        flex-direction: column;
    }

    .service-grid {
        grid-template-columns: 1fr;
    }

    .mode-selector {
        flex-direction: column;
        align-items: center;
    }

    .pc-component-grid {
        grid-template-columns: 1fr;
    }
}

/* Simple Mode PC Builder Styles */
.simple-mode-steps {
    max-width: 800px;
    margin: 0 auto;
}

.step-container {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.step-container:not(.disabled) {
    border-color: rgba(0, 255, 255, 0.3);
    background: rgba(0, 255, 255, 0.05);
}

.step-container.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.step-container h3 {
    color: #00ffff;
    margin-top: 0;
    margin-bottom: 15px;
    text-align: center;
}

.step-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.option-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 7px 5px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    font-size: 0.7em;
}

.option-btn:hover {
    border-color: rgba(0, 255, 255, 0.5);
    background: rgba(0, 255, 255, 0.1);
    transform: translateY(-2px);
}

.option-btn.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.option-icon {
    font-size: 24px;
    margin-bottom: 8px;
}

.option-name {
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}

.option-price {
    color: #ffd700;
    font-size: 14px;
    font-weight: bold;
}

.budget-options {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    padding: 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

.budget-options h4 {
    margin-top: 0;
    margin-bottom: 15px;
}

.base-service-info {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
    border-left: 4px solid #ffc107;
}

.base-service-info h4 {
    color: #ffc107;
    margin-top: 0;
    margin-bottom: 15px;
}

.base-service-info ul {
    list-style-type: none;
    padding-left: 0;
    margin: 10px 0;
}

.base-service-info li {
    padding: 5px 0;
    color: #ccc;
    position: relative;
    padding-left: 20px;
}

.base-service-info li:before {
    content: "✓";
    color: #4CAF50;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Custom Budget Input */
#custom-budget-value {
    width: 100%;
    padding: 10px;
    border: 1px solid #555;
    border-radius: 5px;
    background: #333;
    color: white;
    text-align: center;
    font-size: 16px;
    margin-top: 10px;
}

#custom-budget-value:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

/* Selected Configuration Summary */
.selected-config-summary {
    background: rgba(0, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-top: 30px;
    border: 2px solid rgba(0, 255, 255, 0.3);
}

.config-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.config-item {
    background: rgba(0, 0, 0, 0.2);
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.config-item strong {
    color: #00ffff;
}

.config-total {
    background: rgba(255, 215, 0, 0.1);
    border-radius: 8px;
    padding: 15px;
    border: 2px solid rgba(255, 215, 0, 0.3);
}

/* Labor Fee Details */
.labor-fee-details {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
    padding: 10px;
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 3px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: #2b9869;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e6b4a;
}

/* P
erformance optimizations for frequently animated elements */
.stat-value,
.option-btn,
.service-card,
.component-option {
    transform: translateZ(0);
    /* Create composite layer */
    backface-visibility: hidden;
}

/* Reduce paint operations on hover */
.stat-value:hover,
.option-btn:hover,
.service-card:hover,
.component-option:hover {
    will-change: transform;
}

.stat-value:not(:hover),
.option-btn:not(:hover),
.service-card:not(:hover),
.component-option:not(:hover) {
    will-change: auto;
}

/* ========================================
   WALLET STYLES - Consolidated from KMS_wallet.css
   ======================================== */

/* Amount Grid and Buttons */
.amount-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.amount-btn {
    padding: 15px 10px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
    position: relative;
    overflow: hidden;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.amount-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background: linear-gradient(145deg, #34b57e, #267d5a);
}

.amount-btn.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.amount-value {
    font-size: 18px;
    margin-bottom: 5px;
    color: #ffffff;
}

.amount-label {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

/* Payment Methods Grid */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 5px;
    margin: 15px 0;
}

.payment-methods-row {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin: 15px 0;
    justify-content: space-between;
}

.payment-method {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: #ffc000;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.payment-method-btn {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 5px 10px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: #05c3b6;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
    flex: 1;
    min-width: 120px;
    justify-content: flex-start;
    gap: 8px;
}

.payment-method-btn:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.payment-method-btn.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.payment-icon {
    font-size: 16px;
    margin-right: 8px;
}

.payment-text {
    font-size: 14px;
    font-weight: 500;
}

.payment-method:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.payment-method.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.payment-info {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.payment-name {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.payment-desc {
    color: #e0e0e0;
    font-size: 11px;
    margin-top: 2px;
}

/* Custom Amount Input */
.custom-amount {
    margin: 15px 0;
}

.custom-amount input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background-color: #ffc000;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

.custom-amount input:focus {
    border-color: #00ffff;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
    outline: none;
}

.custom-amount input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Deposit Summary */
.deposit-summary {
    margin: 20px 0;
    padding: 15px;
    border-radius: 10px;
    background: #ffc000;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 16px;
}

/* Copy Code Button */
.copy-code-btn {
    padding: 8px 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.copy-code-btn:hover {
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Withdraw Modal Styles */
#custom-withdraw-amount {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background-color: #ffc000;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

#custom-withdraw-amount:focus {
    border-color: #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    outline: none;
}

#custom-withdraw-amount::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Withdraw Commission Modal */
.withdraw-commission-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    z-index: 1000;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.withdraw-commission-modal h3 {
    color: #00ffff;
    margin-bottom: 15px;
}

.withdraw-commission-modal p {
    margin-bottom: 20px;
    color: white;
}

.withdraw-commission-modal button {
    padding: 10px 20px;
    background: #ffd700;
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.withdraw-commission-modal button:hover {
    background: linear-gradient(145deg, #34b57e, #267d5a);
}

/* Withdraw Commission and Transfer Buttons */
.withdraw-commission-btn,
.transfer-commission-btn {
    padding: 10px 20px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.withdraw-commission-btn:hover,
.transfer-commission-btn:hover {
    background: #00ffff;
    transform: translateY(-2px);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

/* PC Builder Simple Mode Styles */
.simple-mode-steps {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.step-container {
    padding: 20px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: #05c3b6;
    transition: all 0.3s ease;
}

.step-container.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.step-container h3 {
    color: #00ffff;
    margin-bottom: 10px;
    text-align: center;
}

.step-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.option-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: rgba(0, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    min-height: 100px;
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.option-btn:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.option-btn.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.option-icon {
    font-size: 26px;
    margin-bottom: 5px;
}

.option-name {
    color: white;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    margin-bottom: 5px;
}

.option-price {
    color: #ffd700;
    font-size: 12px;
    font-weight: bold;
}

.base-service-info {
    margin-top: 30px;
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
}

.base-service-info h4 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.base-service-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.base-service-info li {
    color: white;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.base-service-info li:before {
    content: "✓ ";
    color: #00ff00;
    font-weight: bold;
    margin-right: 8px;
}

/* PC Builder Detailed Mode Styles */
.detailed-mode-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.component-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.category-section {
    padding: 20px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(43, 152, 105, 0.8), rgba(30, 107, 74, 0.8));
}

.category-section h3 {
    color: #00ffff;
    margin-bottom: 15px;
    text-align: center;
}

.component-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.component-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 150px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.component-option:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.component-option.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.component-name {
    color: white;
    font-weight: bold;
    font-size: 13px;
    text-align: center;
    margin-bottom: 5px;
}

.component-specs {
    color: #e0e0e0;
    font-size: 11px;
    text-align: center;
    margin-bottom: 5px;
}

.component-price {
    color: #ffd700;
    font-size: 12px;
    font-weight: bold;
}

.selected-components {
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
}

.selected-components h3 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.components-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.component-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.component-item-name {
    color: white;
    font-weight: bold;
}

.component-item-price {
    color: #ffd700;
}

.no-selection {
    color: #e0e0e0;
    text-align: center;
    font-style: italic;
    padding: 20px;
}

.components-total {
    margin-top: 20px;
    padding: 10px;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
    text-align: right;
    color: #00ff00;
    font-size: 18px;
}

/* ========================================
   WALLET MODAL STYLES - Consolidated from KMS_wallet_modals.css
   ======================================== */

/* Enhanced Modal Base Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
    -webkit-backdrop-filter: blur(2px);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Modal Header */
.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header h2 {
    margin: 0;
    color: #00ffff;
    font-size: 1.5rem;
}

.close {
    background: none;
    border: none;
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close:hover,
.close:focus {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    outline: none;
}

/* Modal Body */
.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 10px 20px 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Form Styles */
.form-group {
    margin: 20px 0;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #00ffff;
    font-weight: bold;
    font-size: 0.95rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ccc;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.95);
    color: #333;
    box-sizing: border-box;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #00ffff;
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.2);
}

.form-group textarea {
    background-color: #ffc000;
    resize: vertical;
    min-height: 80px;
    font-family: inherit;
}

.form-help {
    display: block;
    margin-top: 5px;
    color: #ccc;
    font-size: 0.85rem;
    font-style: italic;
}

/* Balance Display */
.balance-display {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    text-align: center;
}

.balance-display label {
    color: #00ffff;
    font-weight: bold;
    font-size: 1.1rem;
}

.balance-amount {
    color: #ffd700;
    font-size: 1.3rem;
    font-weight: bold;
}

/* Amount Buttons for Modals */
.amount-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.amount-btn {
    padding: 12px 8px;
    border: 2px solid #2196F3;
    background: rgba(33, 150, 243, 0.1);
    color: #2196F3;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: bold;
    font-size: 0.9rem;
}

.amount-btn:hover {
    background: rgba(33, 150, 243, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(33, 150, 243, 0.3);
}

.amount-btn.selected {
    background: #2196F3;
    color: white;
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.5);
}

.amount-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.5);
}

/* Payment Methods for Modals */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin: 15px 0;
}

.payment-method-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px 10px;
    border: 2px solid #00bcaa;
    background: rgba(0, 188, 170, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    min-height: 70px;
}

.payment-method-btn:hover {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 188, 170, 0.3);
}

.payment-method-btn.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.payment-method-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.5);
}

.payment-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-text {
    font-weight: bold;
    text-align: center;
}

/* Primary Button */
.btn-primary {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
    padding: 14px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1rem;
    width: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(33, 150, 243, 0.4);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-primary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-primary:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.5);
}

.btn-loading {
    display: none;
}

.btn-primary.loading .btn-text {
    display: none;
}

.btn-primary.loading .btn-loading {
    display: inline;
}

/* Notice Styles */
.withdrawal-notice,
.transfer-notice {
    margin-top: 15px;
    padding: 10px;
    background: rgba(255, 193, 7, 0.1);
    border-left: 4px solid #ffc107;
    border-radius: 4px;
}

.withdrawal-notice small,
.transfer-notice small {
    color: #ffc107;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Error States */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #f44336;
    box-shadow: 0 0 0 2px rgba(244, 67, 54, 0.2);
}

.error-message {
    color: #f44336;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
}

/* Success States */
.form-group.success input,
.form-group.success select,
.form-group.success textarea {
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

/* Loading States */
.modal-content.loading {
    pointer-events: none;
    opacity: 0.8;
}

.modal-content.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }

    .wallet-stats {
        flex-direction: column;
    }

    .service-grid {
        grid-template-columns: 1fr;
    }

    .mode-selector {
        flex-direction: column;
        align-items: center;
    }

    .pc-component-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 2% auto;
        max-height: 95vh;
    }

    .amount-buttons {
        grid-template-columns: repeat(3, 1fr);
    }

    .payment-methods {
        grid-template-columns: repeat(2, 1fr);
    }

    .modal-header h2 {
        font-size: 1.3rem;
    }
}

@media (max-width: 480px) {
    .modal-content {
        width: 98%;
        margin: 1% auto;
    }

    .amount-buttons {
        grid-template-columns: repeat(2, 1fr);
    }

    .payment-methods {
        grid-template-columns: 1fr;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    .modal-content,
    .amount-btn,
    .payment-method-btn,
    .btn-primary {
        animation: none;
        transition: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .modal-content {
        border: 2px solid white;
    }

    .amount-btn,
    .payment-method-btn {
        border-width: 3px;
    }
}

/* Focus Visible for Better Accessibility */
.amount-btn:focus-visible,
.payment-method-btn:focus-visible,
.btn-primary:focus-visible,
.close:focus-visible {
    outline: 2px solid #00ffff;
    outline-offset: 2px;
}