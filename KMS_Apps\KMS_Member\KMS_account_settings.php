<?php
/**
 * Member Account Settings Page
 */

// Error reporting and session management
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start();

// Include required files
$base_path = dirname(dirname(__DIR__));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Language' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_language.php';

// Authentication check
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../../index.php');
    exit;
}

// Get user data
$user_id = $_SESSION['id'];
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = execute_query($link, $sql, "i", [$user_id]);

if (!$stmt) {
    die('Error loading user data');
}

$result = mysqli_stmt_get_result($stmt);
$user = mysqli_fetch_assoc($result);
mysqli_stmt_close($stmt);

if (!$user) {
    die('User not found');
}

close_db_connection($link);
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Settings - KelvinKMS</title>
    
    <!-- External CSS - Fully Consolidated -->
    <link rel="stylesheet" href="KMS_CSS/KMS_member.css">
    <link rel="stylesheet" href="../KMS_Index/KMS_Authentication/KMS_CSS/KMS_custom-modal.css">
    
    <style>
        .account-settings-container {
            max-width: 600px;
            margin: auto;
            background-color: #10a4c9;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 2px 4px 6px rgba(0,0,0,0.6);
        }
        
        .settings-header {
            text-align: center;
            margin-bottom: 10px;
        }
        
        .settings-form {
            display: grid;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            color: #00ffff;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .form-group input,
        .form-group select {
            padding: 12px;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            background-color: rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
        }
        
        .form-group input:read-only {
            background-color: rgba(255, 255, 255, 0.3);
            color: #ffffff;
            cursor: not-allowed;
        }
        
        .form-group input:focus:not(:read-only) {
            border-color: #00ffff;
            box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            outline: none;
        }
        
        .editable-field {
            background-color: #ffd500ff !important;
            color: white !important;
        }
        
        .save-button {
            width: 30%;
            justify-self: center;
            padding: 10px 15px;
            margin-top: 10px;
            background: #ffbc00;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 2px 4px 6px rgba(0,0,0,0.6);
        }
        
        .save-button:hover {
            background: linear-gradient(145deg, #45a049, #3d8b40);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }
        
        .back-button {
            position:fixed;
            top: 10px;
            right: 10px;
            padding: 10px 15px;
            background: rgb(33 166 127);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 2px 4px 6px rgba(0,0,0,0.6);
        }
        
        .back-button:hover {
            background: linear-gradient(145deg, #0b7dda, #0a6bc7);
            transform: translateY(-2px);
        }
        
        .contact-info {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid rgba(255, 215, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            text-align: center;
        }
        
        .contact-info h4 {
            color: #ffd700;
            margin-bottom: 10px;
        }
        
        .contact-info p {
            color: white;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="account-settings-container">
        <button class="back-button" onclick="window.location.href='KMS_member.php'">← Back to Dashboard</button>
        
        <div class="settings-header">
            <h1>⚙️ Account Settings</h1>
            <p>Manage your account information</p>
        </div>
        
        <form class="settings-form" id="accountForm">
            <div class="form-row">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" value="<?= htmlspecialchars($user['username']) ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="email">Email</label>
                    <input type="email" id="email" name="email" value="<?= htmlspecialchars($user['email']) ?>" readonly>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="first_name">First Name</label>
                    <input type="text" id="first_name" name="first_name" value="<?= htmlspecialchars($user['first_name']) ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="last_name">Last Name</label>
                    <input type="text" id="last_name" name="last_name" value="<?= htmlspecialchars($user['last_name']) ?>" readonly>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="nickname">Nickname (Editable)</label>
                    <input type="text" id="nickname" name="nickname" value="<?= htmlspecialchars($user['nickname']) ?>" class="editable-field">
                </div>
                <div class="form-group">
                    <label for="gender">Gender</label>
                    <input type="text" id="gender" name="gender" value="<?= htmlspecialchars($user['gender']) ?>" readonly>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="phone_number">Phone Number</label>
                    <input type="text" id="phone_number" name="phone_number" value="<?= htmlspecialchars($user['phone_number']) ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="language">Language</label>
                    <input type="text" id="language" name="language" value="<?= $user['language'] === 'en' ? 'English' : $user['language'] ?>" readonly>
                </div>
            </div>
            
            <div class="form-group">
                <label for="street_address">Street Address</label>
                <input type="text" id="street_address" name="street_address" value="<?= htmlspecialchars($user['street_address']) ?>" readonly>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="city">City</label>
                    <input type="text" id="city" name="city" value="<?= htmlspecialchars($user['city']) ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="state">State</label>
                    <input type="text" id="state" name="state" value="<?= htmlspecialchars($user['state']) ?>" readonly>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="zip_code">ZIP Code</label>
                    <input type="text" id="zip_code" name="zip_code" value="<?= htmlspecialchars($user['zip_code']) ?>" readonly>
                </div>
                <div class="form-group">
                    <label for="created_at">Member Since</label>
                    <input type="text" id="created_at" name="created_at" value="<?= date('F j, Y', strtotime($user['created_at'])) ?>" readonly>
                </div>
            </div>
            
            <button type="submit" class="save-button">💾 Save Changes</button>
        </form>
        
        <div class="contact-info">
            <h4>📞 Need to Update Other Information?</h4>
            <p>For security reasons, most account information can only be changed by contacting our support team.</p>
            <p><strong>Contact us via Live Chat for assistance with:</strong></p>
            <p>• Email address changes • Phone number updates • Address modifications • Name corrections</p>
        </div>
    </div>
    
    <script>
        document.getElementById('accountForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const nickname = document.getElementById('nickname').value.trim();
            
            if (!nickname) {
                alert('Nickname cannot be empty');
                return;
            }
            
            // Send update request
            const formData = new FormData();
            formData.append('action', 'update_nickname');
            formData.append('nickname', nickname);
            
            fetch('KMS_PHP/KMS_update_account.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Nickname updated successfully!');
                } else {
                    alert('Error updating nickname: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating nickname');
            });
        });
    </script>
</body>
</html>
